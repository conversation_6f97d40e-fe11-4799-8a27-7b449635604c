"use client"

import { SignInCard } from "@/features/auth/components/sign-in-card";
import { SignUpCard } from "@/features/auth/components/sign-up-card";
import { useStateManager } from "@/hooks/use-context";
import { redirect } from "next/navigation";

const SignUpPage = () => {
  const { user, hasAnyOrganization, isLoading } = useStateManager();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (user) {
    if (hasAnyOrganization) {
      redirect("/choose-organization");
    } else {
      redirect("/create-organization");
    }
  }

  return <SignUpCard />
}

export default SignUpPage;
"use client"

import { SignInCard } from "@/features/auth/components/sign-in-card";
import { SignUpCard } from "@/features/auth/components/sign-up-card";
import { useStateManager } from "@/hooks/use-context";
import { redirect } from "next/navigation";

const SignUpPage = () => {
  const {user} = useStateManager();

  if (user) {
    redirect("/");
  }

  return <SignUpCard />
}

export default SignUpPage;
"use client"

import { useState } from "react";
import { Building2, Users, ArrowRight, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useStateManager } from "@/hooks/use-context";

const mockOrganizations = [
  {
    id: 1,
    name: "Tech Solutions Inc",
    description: "Software development and consulting",
    memberCount: 15,
    logo: null
  },
  {
    id: 2,
    name: "Marketing Agency Pro",
    description: "Digital marketing and brand management",
    memberCount: 8,
    logo: null
  }
];

export const ChooseOrganizationCard = () => {
  const [selectedOrg, setSelectedOrg] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useStateManager();

  const handleSelectOrganization = async (orgId: number) => {
    
      console.log("Selecting organization:", orgId);
      
  };

  const organizations = user?.organizations || mockOrganizations;

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-2xl bg-white/95 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <Building2 className="w-10 h-10 text-white" />
          </div>
          <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Your Organizations
          </CardTitle>
          <p className="text-gray-600">
            Select an organization to access your dashboard
          </p>
        </CardHeader>
        
        <CardContent className="p-6 md:p-8">
          <div className="grid gap-4 md:gap-6">
            {organizations.map((org) => (
              <div
                key={org.id}
                onClick={() => setSelectedOrg(org.id)}
                className={`
                  relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200
                  ${selectedOrg === org.id 
                    ? 'border-green-400 bg-green-50 shadow-lg' 
                    : 'border-gray-200 hover:border-green-300 hover:bg-green-25'
                  }
                `}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                      {org.logo ? (
                        <img 
                          src={org.logo} 
                          alt={org.name}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <Building2 className="w-6 h-6 text-gray-600" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {org.name}
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">
                        {org.description}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <Users className="w-3 h-3 mr-1" />
                        {org.memberCount} members
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    {selectedOrg === org.id && (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                    <ArrowRight className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {selectedOrg && (
            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => handleSelectOrganization(selectedOrg)}
                disabled={isLoading}
                className="flex-1 h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Accessing...
                  </div>
                ) : (
                  "Access Dashboard"
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create New Organization Option */}
      <Card className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors cursor-pointer">
        <CardContent className="p-6 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
            <Plus className="w-8 h-8 text-blue-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Create New Organization</h3>
          <p className="text-gray-600 text-sm mb-4">
            Start a new organization and invite your team
          </p>
          <Button 
            variant="outline" 
            className="border-blue-400 text-blue-600 hover:bg-blue-50"
            onClick={() => window.location.href = '/setup/create-organization'}
          >
            Create Organization
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

"use client";
import { User } from '@/features/auth/types/auth';
import { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Auth } from '@/features/auth/server/Auth';

interface ContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  handleLogin: (data: any) => void;
  hasAnyOrganization: boolean;
}

const stateManager = createContext<ContextType>({
  isAuthenticated: false,
  user: null,
  isLoading: true,
  handleLogin: () => {},
  hasAnyOrganization: false,
});

export const StateManagerProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false); 
  const [user, setUser] = useState<User | null>(null); 
  const [isLoading, setIsLoading] = useState<boolean>(true); 
  const [hasAnyOrganization, setHasAnyOrganization] = useState<boolean>(false); 

  const handleLogin = (data: any) => {
    console.log(data);
    localStorage.setItem("izi_token", data.token);
    setHasAnyOrganization(data.user.organizations.length > 0);
    setIsAuthenticated(true);
    setUser(data.user);
    setIsLoading(false);
  };

  // checking authentication status on page load by calling the getUser API
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const token = localStorage.getItem("izi_token");
        
        if (!token) {
          setIsAuthenticated(false);
          setUser(null);
          setIsLoading(false);
          return;
        }

        const response = await Auth.getUser(token);
        
        if (response && response.data) {
          setUser(response.data.user);
          setIsAuthenticated(true);
          setHasAnyOrganization(response.data.user.organizations.length > 0);
        } else {
          // Invalid token, remove it
          localStorage.removeItem("izi_token");
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        // Remove invalid token
        localStorage.removeItem("izi_token");
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  return (
    <stateManager.Provider
      value={{
        isAuthenticated,
        user,
        isLoading,
        handleLogin,
        hasAnyOrganization
      }}
    >
      {children}
    </stateManager.Provider>
  );
};

export const useStateManager = () => useContext(stateManager);

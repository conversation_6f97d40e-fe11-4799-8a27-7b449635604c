"use client";

import { useStateManager } from "@/hooks/use-context";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";

export default function AuthWrapper({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useStateManager();
  const router = useRouter();
  const pathname = usePathname();

 // auth routes
  const isAuthRoute = pathname.startsWith("/sign-");
  
  // protected routes (routes that need authentication)
  const isProtectedRoute = !isAuthRoute;

  useEffect(() => {
    if (isAuthenticated && isAuthRoute) {
      router.push("/");
    } else if (!isAuthenticated && isProtectedRoute) {
      router.push("/sign-in");
    }
  }, [isAuthenticated, pathname, router, isAuthRoute, isProtectedRoute]);

  return <>{children}</>;
}

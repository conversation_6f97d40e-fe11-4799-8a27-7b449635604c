import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Auth } from "../server/Auth";
import { useStateManager } from "@/hooks/use-context";
import { LoginResponse } from "../types/auth";

export const useLogin = () => {
  const queryClient = useQueryClient();
  const { handleLogin } = useStateManager();

  const mutation = useMutation<void, Error, { phone: string; password: string }>({ 
    mutationFn: async (data: { phone: string; password: string }) => {
      
      const res = await Auth.login(data);
      return res;
    },
    onSuccess: (data) => {
        handleLogin(data);

        queryClient.invalidateQueries({ queryKey: ["auth"] });

      },
      
    onError: (error) => {
      console.log(error.message);
      toast.error(error.message);
    },
  });

  return mutation;
};
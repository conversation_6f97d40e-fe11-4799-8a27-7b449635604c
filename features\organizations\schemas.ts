import { z } from "zod";

export const createOrganizationSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(100, "Name must be less than 100 characters"),
  phone: z.string().min(10, "Phone number is required").max(20, "Phone number is too long"),
  address: z.string().min(1, "Address is required").max(255, "Address must be less than 255 characters"),
  description: z.string().min(1, "Description is required").max(500, "Description must be less than 500 characters"),
  featured_image: z.string().optional(),
});

export type CreateOrganizationFormData = z.infer<typeof createOrganizationSchema>;

import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import axios from "axios";
import { toast } from "sonner";

export class Auth {
  
  public static async login(formdata: {phone: string, password: string}) {
    console.log(formdata)
    try {
      const response = await axios.post(IziApi.login,formdata);

      if (response.status === 200) {
        toast.success("Login Successfully!");
        return response.data.data; 
      }
    } catch (error: any) {
      console.log(error);
      throw error.response?.data || error.message; 
    }
  }

  public static async register(formData: {
    password: string;
    name: string;
    phone: string;
  }) {
    try {
      const response = await axios.post(IziApi.register, formData);

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data;
      }
    } catch (error: any) {
      throw error.response.data;
    }
  }

  public static async getUser(token: string) {
    try {
      const response = await axios.get(IziApi.loggedInUser,
        {
          headers: {
            Authorization:`Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        // toast.success(response.data.message);
        return response.data.data; 
      }
    } catch (error: any) {
      throw error.response.data; 
    }
  }
  
  
}
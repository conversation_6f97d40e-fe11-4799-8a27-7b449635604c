"use client"

import { z } from "zod";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/fc";
import { FaGithub } from "react-icons/fa";
import { zodResolver } from "@hookform/resolvers/zod";

import { DottedSeparator } from "@/components/dotted-separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { FormProvider, useForm } from "react-hook-form";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useEffect, useState } from "react";
import Link from "next/link";
import { Eye, EyeOff } from "lucide-react";

import { Loader2 } from "lucide-react";
import { loginSchema } from "../schemas";
import { useLogin } from "../api/use-login";

import 'react-phone-number-input/style.css'
import PhoneInput from 'react-phone-number-input'
import { cn } from "@/lib/utils";

const formSchema = loginSchema;

export const SignInCard = () => {
    const [showPassword, setShowPassword] = useState(false);

    const login = useLogin();

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            phone: "",
            password: "",
        }
    });

    const onSubmit = (data: z.infer<typeof formSchema>) => {
        login.mutate(data);
    }

    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    if (!mounted) {
        return null;
    }

    return (
        <Card className="w-full max-w-md lg:max-w-lg border-none shadow-2xl bg-white/90 backdrop-blur-sm">
            <CardHeader className="text-center p-8 pb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                </div>
                <CardTitle className="text-3xl font-bold text-primary mb-3">Welcome Back</CardTitle>
                <p className="text-muted-foreground">Sign in to continue managing your shop</p>
            </CardHeader>
            
            <CardContent className="p-8 pt-0">
                <FormProvider {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            name="phone"
                            control={form.control}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-primary font-medium">Phone Number</FormLabel>
                                    <FormControl>
                                        <PhoneInput
                                            defaultCountry="TZ"
                                            placeholder="Enter phone number"
                                            value={field.value}
                                            onChange={(value) => field.onChange(value || "")}
                                            className={cn(
                                                "flex h-12 w-full rounded-md border border-primary/20 bg-white/50 px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none",
                                                "placeholder:text-muted-foreground",
                                                "disabled:cursor-not-allowed disabled:opacity-50"
                                            )}
                                            style={{
                                                '--PhoneInputCountryFlag-height': '1em',
                                                '--PhoneInputCountrySelectArrow-color': 'var(--muted-foreground)',
                                            }}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="password"
                            control={form.control}
                            render={({ field }) => (
                                <FormItem>
                                    <div className="flex items-center justify-between">
                                        <FormLabel className="text-primary font-medium">Password</FormLabel>
                                        <Link 
                                            href="/forgot-password" 
                                            className="text-sm text-primary hover:text-primary/80 transition-colors"
                                        >
                                            Forgot password?
                                        </Link>
                                    </div>
                                    <FormControl>
                                        <div className="relative">
                                            <Input
                                                {...field}
                                                type={showPassword ? "text" : "password"}
                                                placeholder="Enter your password"
                                                className="h-12 border-primary/20 focus:border-primary focus:ring-primary/20 bg-white/50 pr-12"
                                            />
                                            <button
                                                type="button"
                                                onClick={() => setShowPassword(!showPassword)}
                                                className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-primary transition-colors"
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-5 w-5" />
                                                ) : (
                                                    <Eye className="h-5 w-5" />
                                                )}
                                            </button>
                                        </div>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <Button 
                            size="lg" 
                            disabled={login.isPending}
                            className="w-full h-12 bg-primary hover:bg-primary/90 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                        >
                            {login.isPending ? 'Signing in' : 'Sign In to Dashboard'}
                        </Button>
                    </form>
                </FormProvider>
                
                <div className="mt-8">
                    <DottedSeparator />
                </div>

                <div className="text-center mt-6">
                    <p className="text-sm text-muted-foreground">
                        Don&apos;t have an account?{" "}
                        <Link href="/sign-up" className="text-primary hover:text-primary/80 font-medium transition-colors">
                            Create Account
                        </Link>
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}

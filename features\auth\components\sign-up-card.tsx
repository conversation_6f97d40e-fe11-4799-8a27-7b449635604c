"use client"

import { z } from "zod";
import { Fc<PERSON>oogle } from "react-icons/fc";
import { FaGithub } from "react-icons/fa";
import { zodResolver } from "@hookform/resolvers/zod";

import { DottedSeparator } from "@/components/dotted-separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { FormProvider, useForm } from "react-hook-form";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useEffect, useState } from "react";
import Link from "next/link";


import { Loader2 } from "lucide-react";
import { registerSchema } from "../schemas";

const formSchema = registerSchema;

export const SignUpCard = () => {

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            email: "",
            password: "",
        }
    });

    const onSubmit = (data: z.infer<typeof formSchema>) => {
        // register.mutate({
        //     json: {
        //          name: data.name,
        //         email: data.email,
        //         password: data.password,
        //     }
        // });
    }

    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    if (!mounted) {
        return null;
    }

    return (
        <Card className="w-full max-w-md lg:max-w-lg border-none shadow-2xl bg-white/90 backdrop-blur-sm">
            <CardHeader className="text-center p-8 pb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                </div>
                <CardTitle className="text-3xl font-bold text-primary mb-3">Welcome</CardTitle>
                <p className="text-muted-foreground">Sign up to start managing your shop</p>
            </CardHeader>
            
            <CardContent className="p-8 pt-0">
                <FormProvider {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            name="name"
                            control={form.control}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-primary font-medium">Name</FormLabel>
                                    <FormControl>
                                        <Input
                                            {...field}
                                            type="name"
                                            placeholder="Enter your name"
                                            className="h-12 border-primary/20 focus:border-primary focus:ring-primary/20 bg-white/50"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="email"
                            control={form.control}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-primary font-medium">Email Address</FormLabel>
                                    <FormControl>
                                        <Input
                                            {...field}
                                            type="email"
                                            placeholder="Enter your email"
                                            className="h-12 border-primary/20 focus:border-primary focus:ring-primary/20 bg-white/50"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="password"
                            control={form.control}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-primary font-medium">Password</FormLabel>
                                    <FormControl>
                                        <Input
                                            {...field}
                                            type="password"
                                            placeholder="Enter your password"
                                            className="h-12 border-primary/20 focus:border-primary focus:ring-primary/20 bg-white/50"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <Button 
                            size="lg" 
                            className="w-full h-12 bg-primary hover:bg-primary/90 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                        >
                            Create an Account
                        </Button>
                    </form>
                </FormProvider>
                
                <div className="mt-8">
                    <DottedSeparator />
                </div>

                <div className="text-center mt-6">
                    <p className="text-sm text-muted-foreground">
                        Already have an account?{" "}
                        <Link href="/sign-in" className="text-primary hover:text-primary/80 font-medium transition-colors">
                            Login
                        </Link>
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}

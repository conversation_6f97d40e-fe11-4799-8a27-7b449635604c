"use client";

import { useStateManager } from '@/hooks/use-context';
import Image from 'next/image';
import React from 'react';
import { Button } from "@/components/ui/button";

interface LayoutProps {
    children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
    const { isLoading } = useStateManager();
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading...</p>
                </div>
            </div>
        );
    }

    return (
        <div>
            

            {children}

        </div>
    );
}

export default Layout;
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";

import { cn } from "@/lib/utils";

import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { StateManagerProvider } from "@/hooks/use-context";
import AuthWrapper from "@/components/auth-wrapper";
import { QueryProvider } from "@/components/query-provider";

const inter = Inter({ subsets: ["latin"] });


export const metadata: Metadata = {
  title: "Izi Sales",
  description: "Izi Sales Web version.",
};


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={cn(inter.className, "antialiased min-h-screen")}
      >
        <QueryProvider>
        <StateManagerProvider>
          <AuthWrapper>
            {children}
            <Toaster richColors />
          </AuthWrapper>
        </StateManagerProvider>
        </QueryProvider>
      </body>
    </html>
  );
}

